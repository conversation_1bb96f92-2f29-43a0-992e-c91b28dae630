import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/enum.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/providers/blocked_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/bottom_sheets/other_profile_bottom_sheet.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class BlockedProfile extends ConsumerStatefulWidget {
  const BlockedProfile({
    super.key,
    this.useBackButton = true,
    required this.containerWidth,
    required this.artist,
    this.onAlbumRefresh,
    this.onArtistBlocked,
  });

  final bool useBackButton;
  final double containerWidth;
  final ArtistData artist;
  final Function? onAlbumRefresh;
  final Function? onArtistBlocked;

  @override
  BlockedProfileState createState() => BlockedProfileState();
}

class BlockedProfileState extends ConsumerState<BlockedProfile> {
  final _scrollController = ScrollController();
  bool _doingUnblock = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: <Widget>[
          PmSliverAppBar(
            scrollController: _scrollController,
            automaticallyImplyLeading: widget.useBackButton,
            backgroundColor: context.colors.lightColor,
            titleText: widget.useBackButton ? '@${widget.artist.nicename}' : '',
            useLogo: widget.useBackButton ? false : true,
            actions: [
              ProfileMenuIndicator(
                onTap: () {
                  _showOtherProfileBottomSheet();
                },
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Container(
              color: context.colors.lightColor,
              padding: const EdgeInsets.only(
                top: LayoutConfig.contentTopGap,
                right: ScreenStyleConfig.horizontalPadding,
                bottom: ScreenStyleConfig.verticalPadding + 20.0,
                left: ScreenStyleConfig.horizontalPadding,
              ),
              child: ProfileHeader(
                artist: widget.artist,
                showPhotoMeta: false,
                showLocation: false,
                showWebsite: false,
                showFollowers: false,
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: ScreenStyleConfig.horizontalPadding + 30.0,
                vertical: ScreenStyleConfig.verticalPadding + 20.0,
              ),
              child: Text(
                '@${widget.artist.nicename} is blocked.',
                style: TextStyle(
                  fontSize: 16.0,
                  color: context.isDarkMode
                      ? AppColorsCache.dark().greyColor
                      : AppColorsCache.light().brandColorAlt,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showOtherProfileBottomSheet() {
    final ArtistData artist = widget.artist.copyWith(
      isBlocked: ref.read(artistFieldProvider(widget.artist.id)).isBlocked,
    );

    showModalBottomSheet(
      context: context,
      isDismissible: !_doingUnblock,
      // isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return OtherProfileBottomSheet(
          artist: artist,
          onUnblockArtist: _unblockArtist,
        );
      },
    );
  }

  Future<void> _unblockArtist() async {
    setState(() {
      _doingUnblock = true;
    });

    final artistService = ArtistService();
    final BaseResponse response = await artistService.blockUnblockArtist(
      artistId: widget.artist.id,
      action: BlockActionType.unblock,
    );

    if (mounted) {
      setState(() {
        _doingUnblock = false;
      });
    }

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }

        Navigator.pop(context);
      }

      return;
    }

    ref
        .read(artistStoreProvider.notifier)
        .setIsBlocked(widget.artist.id, false);

    ref.read(blockedArtistIdsProvider.notifier).removeItem(widget.artist.id);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );
    }
  }
}
