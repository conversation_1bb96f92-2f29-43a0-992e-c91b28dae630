import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/load_more/enum.dart';
import 'package:portraitmode/load_more/masonry_load_more.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';

class AlbumTabContentItem extends ConsumerStatefulWidget {
  const AlbumTabContentItem({
    super.key,
    required this.containerWidth,
    required this.artistId,
    required this.album,
    this.onAlbumRefresh,
  });

  final double containerWidth;
  final int artistId;
  final AlbumData album;
  final Function? onAlbumRefresh;

  @override
  AlbumTabContentItemState createState() => AlbumTabContentItemState();
}

class AlbumTabContentItemState extends ConsumerState<AlbumTabContentItem>
    with AutomaticKeepAliveClientMixin<AlbumTabContentItem> {
  final PhotoListService photoListService = PhotoListService();
  late NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
  _myAlbumPhotoListProvider;

  static const _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  @override
  bool wantKeepAlive = true;

  @override
  void initState() {
    _myAlbumPhotoListProvider = getMyAlbumPhotoListProvider(widget.album.slug);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    List<PhotoData> photoList = ref.watch(_myAlbumPhotoListProvider);

    return SafeArea(
      top: false,
      bottom: false,
      child: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: Builder(
          // This Builder is needed to provide a BuildContext that is
          // "inside" the NestedScrollView, so that
          // sliverOverlapAbsorberHandleFor() (in MyProfile screen)
          // can find the NestedScrollView.
          builder: (BuildContext context) {
            return MasonryLoadMore(
              isFinished: _loadMoreEndReached,
              onLoadMore: _handleLoadMore,
              loadingWidgetColor: context.colors.baseColorAlt,
              runOnEmptyResult: true,
              loadingStatusText: "",
              finishedStatusText: "",
              padding: const EdgeInsets.symmetric(
                vertical: 8.0,
                horizontal: 8.0,
              ),
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
              itemsCount: photoList.length,
              itemBuilder: (BuildContext masonryContext, int index) {
                if (index >= photoList.length) {
                  return const SizedBox.shrink();
                }

                return PhotoMasonryItem(
                  key: ValueKey(photoList[index].id),
                  index: index,
                  photo: photoList[index],
                  isOwnProfile: true,
                  screenName: 'my_profile',
                  onPhotoTap: () => _handlePhotoTap(photoList[index]),
                );
              },
            );
          },
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: 'my_profile'),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    if (widget.onAlbumRefresh != null) {
      await widget.onAlbumRefresh!();
    }

    final PhotoListResponse response = await photoListService.fetch(
      albumSlug: widget.album.slug,
      limit: _loadMorePerPage,
      lastId: 0,
      artistId: widget.artistId,
    );

    _handlePhotoListResponse(response, true);
  }

  Future<LoadMoreResult> _handleLoadMore() async {
    final PhotoListResponse response = await photoListService.fetch(
      albumSlug: widget.album.slug,
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      artistId: widget.artistId,
    );

    _handlePhotoListResponse(response, false);

    if (!response.success) {
      return LoadMoreResult.failed;
    }

    if (response.data.isEmpty || response.data.length < _loadMorePerPage) {
      return LoadMoreResult.finished;
    }

    return LoadMoreResult.success;
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    final isFirstLoad = _loadMoreLastId == 0;
    _loadMoreLastId = response.data.last.id;

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort response.data (photo list) before consuming it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref.read(_myAlbumPhotoListProvider.notifier).replaceAll(response.data);
      return;
    }

    if (isFirstLoad) {
      ref.read(_myAlbumPhotoListProvider.notifier).replaceAll(response.data);
      return;
    }

    ref.read(_myAlbumPhotoListProvider.notifier).addItems(response.data);
  }
}
