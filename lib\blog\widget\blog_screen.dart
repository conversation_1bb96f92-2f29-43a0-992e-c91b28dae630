import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/responses/blog_post_fetch_response.dart';
import 'package:portraitmode/blog/services/blog_post_service.dart';
import 'package:portraitmode/blog/widget/blog_post_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/max_width/max_width.dart';

class BlogScreen extends ConsumerStatefulWidget {
  const BlogScreen({super.key});

  @override
  BlogScreenState createState() => BlogScreenState();
}

class BlogScreenState extends ConsumerState<BlogScreen> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  final int _loadMorePerPage = 10;
  int _currentPageNumber = 1;
  bool _loadMoreEndReached = false;
  List<BlogPostData> _blogPostList = [];

  @override
  void dispose() {
    _blogPostList = [];
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MaxWidth(
        maxWidth: 768.0,
        child: RefreshIndicator(
          key: _refreshIndicatorKey,
          onRefresh: _handleRefresh,
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              PmSliverAppBar(
                scrollController: _scrollController,
                automaticallyImplyLeading: true,
                useLogo: false,
                titleText: "Blog",
                // actions: const [],
              ),
              EasyLoadMore(
                isFinished: _loadMoreEndReached,
                onLoadMore: _handleLoadMore,
                loadingWidgetColor: context.colors.baseColorAlt,
                runOnEmptyResult: true,
                idleStatusText: "",
                loadingStatusText: "",
                finishedStatusText: "",
                child: _buildSliverListView(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView() {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        if (index >= _blogPostList.length) {
          return const SizedBox.shrink();
        }

        double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;

        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: ScreenStyleConfig.horizontalPadding,
          ),
          margin: EdgeInsets.only(top: marginTop),
          child: BlogPostListItem(
            post: _blogPostList[index],
            borderRadius: PhotoStyleConfig.borderRadius,
          ),
        );
      }, childCount: _blogPostList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _currentPageNumber = 1;
    _loadMoreEndReached = false;

    BlogPostFetchResponse response = await BlogPostService().fetch(
      perPage: _loadMorePerPage,
      page: _currentPageNumber,
    );

    _handlePhotoListResponse(response, true);
  }

  Future<bool> _handleLoadMore() async {
    BlogPostFetchResponse response = await BlogPostService().fetch(
      perPage: _loadMorePerPage,
      page: _currentPageNumber,
    );

    _handlePhotoListResponse(response, false);

    return response.success;
  }

  void _handlePhotoListResponse(
    BlogPostFetchResponse response,
    bool isRefresh,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    final isFirstLoad = _currentPageNumber == 1;
    _currentPageNumber++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        if (isRefresh) {
          _blogPostList = response.data;
        } else {
          if (isFirstLoad) {
            _blogPostList = response.data;
          } else {
            _blogPostList.addAll(response.data);
          }
        }
      });
    }
  }
}
