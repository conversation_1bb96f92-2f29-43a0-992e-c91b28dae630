import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/archived_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/profile/services/profile_service.dart';

class ArchivedPhotosScreen extends ConsumerStatefulWidget {
  const ArchivedPhotosScreen({super.key});

  @override
  ArchivedPhotosScreenState createState() => ArchivedPhotosScreenState();
}

class ArchivedPhotosScreenState extends ConsumerState<ArchivedPhotosScreen> {
  final _scrollController = ScrollController();

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final _profileService = ProfileService();

  @override
  void dispose() {
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    List<PhotoData> photoList = ref.watch(archivedPhotosProvider);

    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            edgeOffset: LayoutConfig.bottomNavBarHeight,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return CustomScrollView(
                  controller: _scrollController,
                  cacheExtent: _cacheExtent,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  slivers: <Widget>[
                    PmSliverAppBar(
                      scrollController: _scrollController,
                      titleText: "Archived photos",
                      useLogo: false,
                      automaticallyImplyLeading: true,
                      actions: const [],
                    ),
                    EasyLoadMore(
                      isFinished: _loadMoreEndReached,
                      onLoadMore: _handleLoadMore,
                      loadingWidgetColor: context.colors.baseColorAlt,
                      runOnEmptyResult: true,
                      loadingStatusText: "",
                      finishedStatusText: "",
                      child: _buildSliverListView(photoList),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView(List<PhotoData> photoList) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;

        return Container(
          margin: EdgeInsets.only(top: marginTop),
          child: PhotoListItem(
            index: index,
            photo: photoList[index],
            isOwnProfile: true,
            screenName: 'archived_photos_screen',
            onTwoFingersOn: () {
              _blockScrollNotifier.value = true;
            },
            onTwoFingersOff: () {
              _blockScrollNotifier.value = false;
            },
          ),
        );
      }, childCount: photoList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    PhotoListResponse response = await _profileService.archivedPhotos(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
    );

    _handlePhotoListResponse(response, true);
  }

  Future<bool> _handleLoadMore() async {
    PhotoListResponse response = await _profileService.archivedPhotos(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
    );

    return _handlePhotoListResponse(response, false);
  }

  bool _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return false;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return false;
    }

    if (response.data.isEmpty) {
      if (isRefresh) {
        ref.read(archivedPhotoIdsProvider.notifier).replaceAll([]);
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return true;
    }

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    final isFirstLoad = _loadMoreLastId == 0;
    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(archivedPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      return true;
    }

    if (isFirstLoad) {
      ref
          .read(archivedPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      return true;
    }

    ref
        .read(archivedPhotoIdsProvider.notifier)
        .addItems(photoListToIdList(response.data));

    return true;
  }
}
