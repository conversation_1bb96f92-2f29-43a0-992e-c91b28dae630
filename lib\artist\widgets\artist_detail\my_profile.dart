import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tab_content_item.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tabbar_delegate.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/settings/widgets/settings_screen.dart';

class MyProfile extends ConsumerWidget {
  const MyProfile({
    super.key,
    this.useBackButton = true,
    required this.scrollController,
    required this.containerWidth,
    required this.artist,
    this.onAlbumRefresh,
  });

  final bool useBackButton;
  final ScrollController scrollController;
  final double containerWidth;
  final ArtistData artist;
  final Function? onAlbumRefresh;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final albumList = ref.watch(myAlbumProvider);

    return DefaultTabController(
      length: albumList.length,
      child: Scaffold(
        body: SafeArea(
          child: NestedScrollView(
            controller: scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
                  return [
                    SliverOverlapAbsorber(
                      handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                        context,
                      ),
                    ),
                    // This PmSliverAppBar was originally inside of the
                    // SliverOverlapAbsorber under it's sliver property.
                    // I extracted it out because it was causing weird top gap.
                    PmSliverAppBar(
                      scrollController: scrollController,
                      automaticallyImplyLeading: useBackButton,
                      backgroundColor: context.colors.lightColor,
                      titleText: useBackButton ? '@${artist.nicename}' : '',
                      useLogo: useBackButton ? false : true,
                      actions: [
                        ProfileMenuIndicator(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsScreen(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    SliverToBoxAdapter(
                      child: Container(
                        color: context.colors.lightColor,
                        padding: const EdgeInsets.only(
                          top: LayoutConfig.contentTopGap,
                          right: ScreenStyleConfig.horizontalPadding,
                          bottom: ScreenStyleConfig.verticalPadding + 20.0,
                          left: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: ProfileHeader(artist: artist),
                      ),
                    ),
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: AlbumTabbarDelegate(albumList: albumList),
                    ),
                  ];
                },
            body: TabBarView(
              children: <AlbumTabContentItem>[
                // Build AlbumTabContent widget using for loop.
                for (AlbumData album in albumList)
                  AlbumTabContentItem(
                    containerWidth: containerWidth,
                    artistId: artist.id,
                    album: album,
                    onAlbumRefresh: onAlbumRefresh,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
