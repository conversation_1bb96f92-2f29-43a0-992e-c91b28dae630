import 'package:flutter/material.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/image_pinch_zooming/image_pinch_zooming.dart';

class PhotoDetailImage extends StatelessWidget {
  const PhotoDetailImage({
    super.key,
    required this.photoUrl,
    required this.photoUrlLarge,
    required this.height,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.zoomable = false,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
  });

  final String photoUrl;
  final String photoUrlLarge;
  final double height;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final bool zoomable;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;

  @override
  Widget build(BuildContext context) {
    return (zoomable ? _buildZoomablePhoto() : _buildUnzoomablePhoto());
  }

  Widget _buildZoomablePhoto() {
    return ImagePinchZooming(
      image: _buildPhoto(),
      // hideStatusBarWhileZooming: true,
      onTap: () {
        onTap?.call();
      },
      onDoubleTap: () {
        onDoubleTap?.call();
      },
      onTwoFingersOn: () {
        onTwoFingersOn?.call();
      },
      onTwoFingersOff: () {
        onTwoFingersOff?.call();
      },
    );
  }

  Widget _buildUnzoomablePhoto() {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      child: _buildPhoto(),
    );
  }

  Widget _buildPhoto() {
    return PmNetworkImage(
      url: photoUrlLarge,
      alignment: const Alignment(-1.0, -1.0),
      loadingWidget: PmNetworkImage(
        url: photoUrl,
        alignment: const Alignment(-1.0, -1.0),
      ),
    );
  }
}
